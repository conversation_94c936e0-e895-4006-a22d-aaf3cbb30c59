package io.gigsta.presentation.email.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import io.gigsta.domain.model.InputMethod
import io.gigsta.presentation.theme.Spacing
import io.gigsta.utils.rememberImageFilePicker

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JobInfoInputSection(
    inputMethod: InputMethod,
    jobDescription: String,
    onJobDescriptionChange: (String) -> Unit,
    onInputMethodChange: (InputMethod) -> Unit,
    onImageSelected: (ByteArray, String, String) -> Unit,
    hasJobImage: Boolean,
    jobImageData: ByteArray?,
    error: String?,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Text(
            text = "2. Informasi Lowongan Pekerjaan",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold
        )
        
        // Input method selector
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            FilterChip(
                onClick = { onInputMethodChange(InputMethod.TEXT) },
                label = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Description,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(Spacing.extraSmall))
                        Text("Teks")
                    }
                },
                selected = inputMethod == InputMethod.TEXT,
                modifier = Modifier.weight(1f)
            )
            
            FilterChip(
                onClick = { onInputMethodChange(InputMethod.IMAGE) },
                label = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Image,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(Spacing.extraSmall))
                        Text("Gambar")
                    }
                },
                selected = inputMethod == InputMethod.IMAGE,
                modifier = Modifier.weight(1f)
            )
        }
        
        // Input content based on selected method
        when (inputMethod) {
            InputMethod.TEXT -> {
                OutlinedTextField(
                    value = jobDescription,
                    onValueChange = onJobDescriptionChange,
                    label = { Text("Deskripsi Lowongan") },
                    placeholder = { Text("Masukkan deskripsi lowongan pekerjaan...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 120.dp),
                    minLines = 5,
                    maxLines = 10,
                    isError = error != null
                )
            }
            
            InputMethod.IMAGE -> {
                JobImageUploadCard(
                    onImageSelected = onImageSelected,
                    hasImage = hasJobImage,
                    imageData = jobImageData,
                    isError = error != null
                )
            }
        }
        
        // Error message
        error?.let { errorMessage ->
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = Spacing.medium)
            )
        }
    }
}

@Composable
private fun JobImageUploadCard(
    onImageSelected: (ByteArray, String, String) -> Unit,
    hasImage: Boolean,
    imageData: ByteArray?,
    isError: Boolean,
    modifier: Modifier = Modifier
) {
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var showEnlargedImage by remember { mutableStateOf(false) }

    val pickImage = rememberImageFilePicker(
        onFileSelected = { selectedFile ->
            onImageSelected(selectedFile.data, selectedFile.name, selectedFile.mimeType)
            errorMessage = null
        },
        onError = { error ->
            errorMessage = error
        }
    )

    // Convert ByteArray to ImageBitmap and get image info
    val imageBitmap = remember(imageData) {
        imageData?.let { data ->
            try {
                // Use the expect/actual pattern for platform-specific implementation
                createImageBitmapFromByteArray(data)
            } catch (e: Exception) {
                println("Failed to create ImageBitmap: ${e.message}")
                null
            }
        }
    }

    val imageInfo = remember(imageData) {
        imageData?.let { data ->
            val sizeInKB = data.size / 1024
            val sizeText = if (sizeInKB < 1024) {
                "${sizeInKB} KB"
            } else {
                "${sizeInKB / 1024} MB"
            }
            sizeText
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .then(
                if (isError) {
                    Modifier.border(
                        1.dp,
                        MaterialTheme.colorScheme.error,
                        RoundedCornerShape(12.dp)
                    )
                } else Modifier
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (hasImage) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            if (hasImage && imageBitmap != null) {
                // Show actual image preview (clickable to enlarge)
                Image(
                    bitmap = imageBitmap,
                    contentDescription = "Preview gambar lowongan",
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 200.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .clickable { showEnlargedImage = true },
                    contentScale = ContentScale.Fit
                )

                Text(
                    text = "Preview Gambar Lowongan",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = "Ketuk untuk memperbesar",
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                imageInfo?.let { info ->
                    Text(
                        text = "Ukuran: $info",
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else if (hasImage && imageInfo != null) {
                // Fallback: Show upload success indicator if image can't be displayed
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = "Gambar Berhasil Diunggah",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = "Ukuran: $imageInfo",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                // Show upload icon and text
                Icon(
                    imageVector = if (hasImage) Icons.Default.Image else Icons.Default.CloudUpload,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = if (hasImage) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                Text(
                    text = if (hasImage) {
                        "Gambar lowongan berhasil diunggah"
                    } else {
                        "Upload Poster Lowongan"
                    },
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    color = if (hasImage) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            
            if (!hasImage) {
                Text(
                    text = "Format yang didukung: PNG, JPG, JPEG\nMaksimal 5MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                
                Button(
                    onClick = pickImage,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Pilih Gambar")
                }
            } else {
                OutlinedButton(
                    onClick = pickImage,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Ganti Gambar")
                }
            }

            // Error message
            errorMessage?.let { error ->
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
        }
    }

    // Enlarged image dialog
    if (showEnlargedImage && imageBitmap != null) {
        EnlargedImageDialog(
            imageBitmap = imageBitmap,
            onDismiss = { showEnlargedImage = false }
        )
    }
}

@Composable
private fun EnlargedImageDialog(
    imageBitmap: ImageBitmap,
    onDismiss: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.9f))
                .clickable { onDismiss() },
            contentAlignment = Alignment.Center
        ) {
            // Close button
            IconButton(
                onClick = onDismiss,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Tutup",
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }

            // Enlarged image
            Image(
                bitmap = imageBitmap,
                contentDescription = "Gambar lowongan diperbesar",
                modifier = Modifier
                    .fillMaxSize()
                    .padding(32.dp),
                contentScale = ContentScale.Fit
            )
        }
    }
}

/**
 * Platform-specific function to create ImageBitmap from ByteArray
 */
expect fun createImageBitmapFromByteArray(byteArray: ByteArray): ImageBitmap?
